{"name": "stackflow", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@codesandbox/sandpack-react": "^2.20.0", "@geist-ui/core": "^2.3.8", "@google/generative-ai": "^0.2.0", "@headlessui/react": "^2.2.4", "@okteto/react-oauth2-login": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@react-oauth/google": "^0.12.2", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "convex": "^1.25.0", "cors": "^2.8.5", "dedent": "^1.6.0", "express": "^5.1.0", "framer-motion": "^11.0.5", "lucide-react": "^0.469.0", "motion": "^12.19.2", "multer": "^2.0.1", "next": "15.1.4", "next-auth": "^4.24.11", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.1.0", "react-markdown-editor-lite": "^1.3.4", "react-resizable-panels": "^1.0.9", "react-syntax-highlighter": "^15.5.0", "sonner": "^1.4.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^20.11.19", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.17", "css-loader": "^7.1.2", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "postcss": "^8.4.35", "postcss-loader": "^8.1.1", "sass": "^1.89.2", "style-loader": "^4.0.0", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}